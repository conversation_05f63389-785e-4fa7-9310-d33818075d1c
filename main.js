import { createSSRApp } from 'vue'
import { createPinia } from 'pinia'
// import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { piniaPluginPersistedstate } from '@/stores/persist'

import App from './App'
import '@/utils/utils'

export function createApp() {
  const app = createSSRApp(App)
  // 创建 Pinia 实例
  const pinia = createPinia()
  // 使用 pinia-plugin-persistedstate 插件
  pinia.use(piniaPluginPersistedstate)
  // 使用 Pinia
  app.use(pinia)
  return {
    app,
  }
}
