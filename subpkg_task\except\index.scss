.page-container {
  display: flex;
  flex-direction: column;
  border-top: 1rpx solid #eee;
  padding-bottom: env(safe-area-inset-bottom);

  ::v-deep .uni-list {
    padding: 10rpx 30rpx 0;
    border-radius: 16rpx;
    overflow: hidden;
  }

  .scroll-view {
    flex: 1;
    overflow: hidden;
  }

  .picker-value {
    width: 360rpx;
    color: $uni-secondary-color;
    font-size: $uni-font-size-base;
    text-align: right;
  }

  .textarea-wrapper {
    position: relative;
    padding-top: 28rpx;
  }

  .textarea {
    width: 100%;
    height: 260rpx;
    font-size: $uni-font-size-base;
    padding: 20rpx 28rpx;
    background-color: #f4f4f4;
    border-radius: 16rpx;
    box-sizing: border-box;
    color: $uni-main-color;
  }

  .words-count {
    position: absolute;
    bottom: 10rpx;
    right: 30rpx;
    color: $uni-secondary-color;
    font-size: $uni-font-size-small;
  }

  ::v-deep .uni-file-picker {
    margin-top: 28rpx;
  }

  .fixbar {
    padding: 20rpx 0;
    border-top: 1rpx solid #eee;
    background-color: #fff;
  }

  .button {
    width: 690rpx;
    height: 100rpx;
    /* #ifdef APP */
    padding-top: 4rpx;
    /* #endif */
    line-height: 100rpx;
    color: #fff;
    font-size: $uni-font-size-big;
    border-radius: 100rpx;
    background-color: $uni-primary;

    &[disabled],
    &.disabled {
      background-color: #fadcd9;
    }
  }

  .popup-action-sheet {
    margin-bottom: 0;
    border-radius: 32rpx 32rpx 0 0;

    .header {
      margin-top: 4rpx;
      font-size: 32rpx;
      font-weight: 700;
    }

    .checkbox {
      width: 46rpx;
      height: 46rpx;
      display: block;
      transform: scale(0.9);
    }
  }
}
