.page-container {
  display: flex;
  flex-direction: column;
  /* #ifdef H5 */
  height: calc(100vh - 50px);
  /* #endif  */
}

.task-tabbar {
  height: 80rpx;
  padding: calc(env(safe-area-inset-top) + 13px) 60rpx 0;

  /* #ifdef MP-WEIXIN */
  padding: calc(env(safe-area-inset-top) + 35px) 60rpx 0;
  /* #endif */

  /* #ifdef APP */
  padding: calc(env(safe-area-inset-top) + 40px) 60rpx 0;
  /* #endif */

  display: flex;
  align-items: center;
  font-size: $uni-font-size-big;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  color: $uni-secondary-color;

  .tab {
    margin-right: 80rpx;
  }

  .active {
    color: $uni-main-color;
    font-weight: 500;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      left: 50%;
      bottom: -18rpx;
      width: 46rpx;
      height: 8rpx;
      border-radius: 8rpx;
      transform: translate(-50%);
      background-color: $uni-primary;
    }
  }
}

.task-list {
  flex: 1;
  overflow: hidden;

  .task-card {
    padding: 24rpx 30rpx 0;
    background-color: #fff;
    border-radius: 16rpx;
    margin: 15rpx 30rpx 30rpx;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .no {
    font-size: $uni-font-size-base;
    color: $uni-main-color;
    font-weight: 500;
  }

  .status {
    padding: 5rpx 20rpx 3rpx;
    font-size: 24rpx;
    color: $uni-primary;
    border: 2rpx solid $uni-primary;
    border-radius: 60rpx;
  }

  .body {
    padding: 40rpx 0;
    border-bottom: 1rpx solid #f4f4f4;
  }

  .timeline {
    border-left: 2rpx dashed #f4f4f4;
    margin-left: 30rpx;
    padding: 0 30rpx;
    position: relative;

    &::before,
    &::after {
      position: absolute;
      left: -24rpx;
      width: 44rpx;
      height: 44rpx;
      text-align: center;
      line-height: 44rpx;
      font-size: 24rpx;
      color: #fff;
      border-radius: 50%;
      background-color: pink;
    }

    &::before {
      content: '起';
      top: -1rpx;
      background-color: $uni-main-color;
    }

    &::after {
      content: '止';
      bottom: -1rpx;
      background-color: $uni-primary;
    }
  }

  .line {
    font-size: $uni-font-size-small;
    color: $uni-secondary-color;
    margin-top: 30rpx;

    &:first-child {
      margin-top: 0;
    }
  }

  .footer {
    padding: 20rpx 0;
    position: relative;

    &.flex {
      display: flex;
    }
  }

  .label,
  .time {
    font-size: $uni-font-size-small;
    margin-right: 15rpx;
    color: $uni-secondary-color;
  }

  .action {
    position: absolute;
    right: 0;
    top: 50%;
    display: flex;
    align-items: center;
    height: 64rpx;
    padding: 0 40rpx;
    background-color: $uni-primary;
    color: #fff;
    font-size: $uni-font-size-small;
    border-radius: 64rpx;
    transform: translate(0, -50%);

    &[disabled],
    &.disabled {
      background-color: #fadcd9;
    }
  }
}

.task-search {
  padding: 30rpx;
  // margin-top: -1rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;

  .search-bar {
    position: relative;

    .icon-search {
      position: absolute;
      top: 22rpx;
      left: 24rpx;
      color: $uni-secondary-color;
      font-size: $uni-font-size-small;
    }

    .input {
      height: 72rpx;
      background-color: #f4f4f4;
      border-radius: 72rpx;
      padding-left: 72rpx;
      font-size: $uni-font-size-small;
    }
  }

  .filter-bar {
    display: flex;
    margin-top: 30rpx;
    font-size: $uni-font-size-small;
    text-align: center;
    line-height: 64rpx;
    color: $uni-secondary-color;

    .picker {
      width: 230rpx;
      height: 64rpx;
      border-radius: 64rpx;
      background-color: $uni-bg-color;
    }

    .text {
      margin: 0 24rpx;
    }

    .button {
      width: 120rpx;
      height: 64rpx;
      padding: 0;
      margin-left: 40rpx;
      line-height: 64rpx;
      border-radius: 64rpx;
      font-size: $uni-font-size-small;
      color: #fff;
      background-color: $uni-primary;

      &[disabled] {
        background-color: #fadcd9;
      }
    }
  }
}

.task-blank {
  position: absolute;
  left: 50%;
  top: 40%;
  width: 201rpx;
  text-align: center;
  padding-top: 130rpx;
  background-image: url(https://sl-driver.oss-cn-hangzhou.aliyuncs.com/images/blank%402x.png);
  background-size: contain;
  background-repeat: no-repeat;
  font-size: 24rpx;
  color: $uni-secondary-color;
  transform: translate(-50%, -100%);
}
