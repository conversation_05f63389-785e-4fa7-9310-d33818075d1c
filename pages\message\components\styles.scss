.message-action {
  padding: 30rpx 44rpx 30rpx;
  color: $uni-secondary-color;
  font-size: 28rpx;

  .icon-clear {
    margin-right: 16rpx;
    font-size: 96%;
  }
}

.scroll-view {
  height: 100%;
  overflow: hidden;
}

.dot {
  display: block;
  width: 16rpx;
  height: 16rpx;
  background-color: $uni-primary;
  border-radius: 50%;
  align-self: center;
  margin-right: 10rpx;
  position: absolute;
  left: 24rpx;
}

::v-deep .uni-list {
  margin: 0 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}

::v-deep .uni-list-item__content-title {
  font-size: 30rpx !important;
  padding-left: 20rpx;
}

::v-deep .uni-list-item__extra-text {
  font-size: 28rpx !important;
  color: $uni-secondary-color !important;
}

::v-deep .uni-card {
  margin-top: 0 !important;
  padding: 0 30rpx !important;
  border-radius: 16rpx;
}

::v-deep .uni-card__content {
  padding: 20rpx 0 !important;
}

.title {
  height: 100rpx;
  font-size: 28rpx;
  color: $uni-main-color;
  font-weight: 600;
  border-bottom: 2rpx solid #eee;
  display: flex;
  align-items: center;
}

.unread::after {
  content: '';
  display: block;
  width: 16rpx;
  height: 16rpx;
  line-height: 1;
  margin-left: 6rpx;
  background-color: $uni-primary;
  border-radius: 50%;
}

.brief {
  margin-top: 20rpx;
  font-size: $uni-font-size-small;
  color: $uni-secondary-color;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  /*设置子元素排列方式*/
  -webkit-box-orient: vertical;
  /*设置显示的行数，多出的部分会显示为...*/
  -webkit-line-clamp: 1;
}

.extra {
  display: flex;
  justify-content: space-between;
  margin: 40rpx 0 10rpx;
  font-size: 28rpx;
}

.time {
  color: $uni-secondary-color;
}

.link {
  display: flex;
  align-items: center;
  height: 44rpx;
  /* #ifdef APP-PLUS */
  line-height: initial;
  /* #endif */
  padding: 0 20rpx;
  color: $uni-primary;
  border: 2rpx solid $uni-primary;
  border-radius: 60rpx;
}

.message-blank {
  position: absolute;
  left: 50%;
  top: 40%;
  width: 201rpx;
  text-align: center;
  padding-top: 130rpx;
  background-image: url(https://sl-driver.oss-cn-hangzhou.aliyuncs.com/images/blank%402x.png);
  background-size: contain;
  background-repeat: no-repeat;
  font-size: 24rpx;
  color: $uni-secondary-color;
  transform: translate(-50%, -100%);
}
