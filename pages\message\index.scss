.page-container {
  display: flex;
  flex-direction: column;
  /* #ifdef H5 */
  height: calc(100vh - 94px);
  /* #endif */
}

.message-tabbar {
  height: 80rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: $uni-font-size-base;
  background-color: #fff;
  color: $uni-secondary-color;
  border-bottom: 1rpx solid #eee;

  .active {
    color: $uni-main-color;
    font-weight: 500;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      left: 50%;
      bottom: -20rpx;
      width: 46rpx;
      height: 8rpx;
      border-radius: 8rpx;
      transform: translate(-50%);
      background-image: linear-gradient(
        210deg,
        #f25c4d 25%,
        #e52d21 100%,
        #e52d21 100%
      );
    }
  }
}

.message-list {
  flex: 1;
  overflow: hidden;
}
